<!-- Games Catalog Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900">
  <!-- Header -->
  <app-header></app-header>

  <!-- Main Content -->
  <div class="pt-28 pb-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      
      <!-- Page Header -->
      <div class="text-start mb-12">
        <h1 class="text-4xl md:text-5xl font-black text-white mb-4">
          Каталог игр
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl ">
          Выберите идеальную игру для вашего мероприятия
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-purple-400 to-pink-400   mt-6 rounded-full"></div>
      </div>

      <!-- Search and Filter Controls -->
      <div class="bg-slate-800/40 backdrop-blur-sm border border-slate-600/50 rounded-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
          <!-- Search -->
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input
                type="text"
                placeholder="Поиск игр..."
                [(ngModel)]="searchTerm"
                (input)="onSearchChange()"
                class="w-full px-4 py-2.5 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors"
              />
              <svg class="absolute right-3 top-3 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- Sort -->
          <div class="flex items-center gap-4">
            <label class="text-gray-300 text-sm">Сортировка:</label>
            <select
              [(ngModel)]="sortBy"
              (change)="onSortChange()"
              class="px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors"
            >
              <option value="-created_at">Новые</option>
              <option value="created_at">Старые</option>
              <option value="title">По названию (А-Я)</option>
              <option value="-title">По названию (Я-А)</option>
              <option value="price">По цене (возрастание)</option>
              <option value="-price">По цене (убывание)</option>
            </select>
          </div>

          <!-- Results count -->
          <div class="text-gray-400 text-sm">
            Найдено: {{ totalGames }} игр
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-20">
        <div class="bg-slate-800/60 backdrop-blur-md border border-purple-400/30 rounded-xl p-6 shadow-2xl">
          <svg class="animate-spin h-10 w-10 text-purple-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
        <p class="text-red-300">{{ error }}</p>
        <button 
          (click)="loadGames()" 
          class="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Games Grid -->
      <div *ngIf="!loading && !error" class="space-y-8">
        <!-- Games Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div 
            *ngFor="let game of games" 
            class="bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer group"
            (click)="viewGameDetails(game.id)"
          >
            <!-- Game Cover Image -->
            <div class="h-48 bg-gradient-to-br from-slate-700 to-slate-800 relative overflow-hidden">
              <img
                *ngIf="game.cover_image"
                [src]="game.cover_image"
                [alt]="game.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              >
              <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center">
                <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                </svg>
              </div>
              
              <!-- Badges -->
              <div class="absolute top-2 left-2 flex flex-col gap-1">
                <span *ngIf="game.trial_available" class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                  Пробная версия
                </span>
                <span *ngIf="game.requires_device" class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">
                  Требует устройство
                </span>
              </div>

              <!-- Library Access Status -->
              <div *ngIf="isInLibrary(game)" class="absolute top-2 right-2">
                <!-- Active Access -->
                <span
                  *ngIf="hasActiveAccess(game)"
                  class="px-2 py-1 bg-green-600 text-white text-xs rounded-full font-medium"
                >
                  Играть
                </span>
                <!-- Expired Access with Renewal Option -->
                <div
                  *ngIf="!hasActiveAccess(game)"
                  class="flex flex-col gap-1"
                >
                  <span class="px-2 py-1 bg-red-600 text-white text-xs rounded-full font-medium">
                    Истёк
                  </span>
                  <span
                    (click)="extendAccess(game)"
                    class="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-full font-medium cursor-pointer transition-colors"
                  >
                    Продлить
                  </span>
                </div>
              </div>
            </div>

            <!-- Game Info -->
            <div class="p-4">
              <h3 class="text-lg font-bold text-white mb-2" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">{{ game.title }}</h3>
              <p *ngIf="game.subtitle" class="text-sm text-gray-300 mb-2" style="display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">{{ game.subtitle }}</p>
              <p class="text-sm text-gray-400 mb-4" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">{{ game.description }}</p>
              
              <!-- Price and Actions -->
              <div class="flex items-center justify-between">
                <span class="text-xl font-bold text-green-400">{{ formatPrice(game.price) }}</span>
                
                <div class="flex gap-2"> 
                  
                  <!-- Action Button -->
                  <button
                    (click)="addToCart(game, $event)"
                    [disabled]="isInCart(game) || canPlay(game)"
                    [class]="canPlay(game)
                      ? 'px-3 py-1.5 bg-green-600 text-white text-sm rounded-lg cursor-not-allowed'
                      : needsAccessExtension(game)
                      ? 'px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors'
                      : isInCart(game)
                      ? 'px-3 py-1.5 bg-gray-600 text-gray-300 text-sm rounded-lg cursor-not-allowed'
                      : 'px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors'"
                  >
                    {{
                      canPlay(game) ? 'Играть' :
                      needsAccessExtension(game) ? 'Продлить' :
                      isInCart(game) ? 'В корзине' : 'Купить'
                    }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="games.length === 0" class="text-center py-20">
          <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0120 12a8 8 0 10-2.343 5.657l.343.343a8 8 0 002-2z"></path>
          </svg>
          <p class="text-gray-400 text-lg">Игры не найдены</p>
          <p class="text-gray-500 text-sm mt-2">Попробуйте изменить параметры поиска</p>
        </div>

        <!-- Pagination -->
        <div *ngIf="games.length > 0 && getTotalPages() > 1" class="flex justify-center items-center gap-4 mt-8">
          <button
            (click)="previousPage()"
            [disabled]="!hasPrevious"
            [class]="hasPrevious ? 'bg-slate-700 hover:bg-slate-600 text-white' : 'bg-slate-800 text-gray-500 cursor-not-allowed'"
            class="px-4 py-2 rounded-lg text-sm transition-colors"
          >
            Назад
          </button>
          
          <span class="text-gray-300 text-sm">
            Страница {{ currentPage }} из {{ getTotalPages() }}
          </span>
          
          <button
            (click)="nextPage()"
            [disabled]="!hasNext"
            [class]="hasNext ? 'bg-slate-700 hover:bg-slate-600 text-white' : 'bg-slate-800 text-gray-500 cursor-not-allowed'"
            class="px-4 py-2 rounded-lg text-sm transition-colors"
          >
            Далее
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
